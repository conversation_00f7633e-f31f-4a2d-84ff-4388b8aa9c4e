/* Archived Scholarships Page Styles */

/* Coming Soon Container */
.coming-soon-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
    margin-bottom: 40px;
}

.coming-soon-content {
    text-align: center;
    max-width: 600px;
    padding: 40px;
    background: linear-gradient(135deg, #ffffff, #f8f9fa);
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid #e9ecef;
}

.coming-soon-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 30px;
    background: linear-gradient(135deg, #1e5631, #2e7d32);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32px;
    color: white;
    box-shadow: 0 8px 25px rgba(30, 86, 49, 0.3);
}

.coming-soon-content h2 {
    margin: 0 0 20px 0;
    font-size: 28px;
    font-weight: 700;
    color: #1e5631;
    background: linear-gradient(135deg, #1e5631, #2e7d32);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.coming-soon-description {
    font-size: 16px;
    color: #666;
    margin-bottom: 30px;
    line-height: 1.6;
}

/* Feature List */
.feature-list {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-bottom: 30px;
    text-align: left;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: rgba(30, 86, 49, 0.05);
    border-radius: 10px;
    border-left: 4px solid #1e5631;
}

.feature-item i {
    color: #1e5631;
    font-size: 16px;
    flex-shrink: 0;
}

.feature-item span {
    color: #333;
    font-weight: 500;
    font-size: 14px;
}

/* Coming Soon Note */
.coming-soon-note {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    padding: 15px 20px;
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    border: 1px solid #ffc107;
    border-radius: 10px;
    margin-bottom: 30px;
}

.coming-soon-note i {
    color: #856404;
    font-size: 16px;
}

.coming-soon-note span {
    color: #856404;
    font-weight: 500;
    font-size: 14px;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

.btn-primary,
.btn-secondary {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    border-radius: 10px;
    text-decoration: none;
    font-weight: 600;
    font-size: 14px;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn-primary {
    background: linear-gradient(135deg, #1e5631, #2e7d32);
    color: white;
    box-shadow: 0 4px 15px rgba(30, 86, 49, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #164023, #1b5e20);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(30, 86, 49, 0.4);
    color: white;
    text-decoration: none;
}

.btn-secondary {
    background: linear-gradient(135deg, #6c757d, #5a6268);
    color: white;
    box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #5a6268, #495057);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4);
    color: white;
    text-decoration: none;
}

/* Placeholder Statistics */
.placeholder-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 40px;
}

.stat-card {
    background: linear-gradient(135deg, #ffffff, #f8f9fa);
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    opacity: 0.6;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    opacity: 0.8;
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: white;
    background: linear-gradient(135deg, #6c757d, #5a6268);
}

.stat-content h3 {
    margin: 0 0 5px 0;
    font-size: 24px;
    font-weight: 700;
    color: #6c757d;
}

.stat-content p {
    margin: 0;
    font-size: 14px;
    color: #999;
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
    .coming-soon-content {
        padding: 30px 20px;
        margin: 0 20px;
    }

    .coming-soon-content h2 {
        font-size: 24px;
    }

    .coming-soon-description {
        font-size: 14px;
    }

    .feature-list {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .feature-item {
        padding: 10px;
    }

    .feature-item span {
        font-size: 13px;
    }

    .action-buttons {
        flex-direction: column;
        align-items: center;
    }

    .btn-primary,
    .btn-secondary {
        width: 100%;
        justify-content: center;
        max-width: 250px;
    }

    .placeholder-stats {
        grid-template-columns: 1fr;
        margin-top: 30px;
    }

    .stat-card {
        padding: 15px;
    }

    .stat-icon {
        width: 40px;
        height: 40px;
        font-size: 16px;
    }

    .stat-content h3 {
        font-size: 20px;
    }

    .coming-soon-icon {
        width: 60px;
        height: 60px;
        font-size: 24px;
        margin-bottom: 20px;
    }

    .coming-soon-note {
        padding: 12px 15px;
        flex-direction: column;
        text-align: center;
        gap: 8px;
    }

    .coming-soon-note span {
        font-size: 13px;
    }
}

@media (max-width: 480px) {
    .coming-soon-content {
        padding: 20px 15px;
    }

    .feature-item {
        font-size: 12px;
    }

    .btn-primary,
    .btn-secondary {
        padding: 10px 20px;
        font-size: 13px;
    }
}
