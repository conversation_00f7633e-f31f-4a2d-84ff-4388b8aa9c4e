<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('archived_students', function (Blueprint $table) {
            // Add remarks column if it doesn't exist
            if (!Schema::hasColumn('archived_students', 'remarks')) {
                $table->text('remarks')->nullable()->after('archive_type');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('archived_students', function (Blueprint $table) {
            if (Schema::hasColumn('archived_students', 'remarks')) {
                $table->dropColumn('remarks');
            }
        });
    }
};
