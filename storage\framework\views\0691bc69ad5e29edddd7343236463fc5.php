<?php $__env->startSection('title', 'Announcements Management'); ?>

<?php $__env->startSection('breadcrumbs'); ?>
    <?php if (isset($component)) { $__componentOriginale19f62b34dfe0bfdf95075badcb45bc2 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale19f62b34dfe0bfdf95075badcb45bc2 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.breadcrumb','data' => ['items' => [['title' => 'Announcements', 'icon' => 'fas fa-bullhorn']]]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('breadcrumb'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['items' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([['title' => 'Announcements', 'icon' => 'fas fa-bullhorn']])]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale19f62b34dfe0bfdf95075badcb45bc2)): ?>
<?php $attributes = $__attributesOriginale19f62b34dfe0bfdf95075badcb45bc2; ?>
<?php unset($__attributesOriginale19f62b34dfe0bfdf95075badcb45bc2); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale19f62b34dfe0bfdf95075badcb45bc2)): ?>
<?php $component = $__componentOriginale19f62b34dfe0bfdf95075badcb45bc2; ?>
<?php unset($__componentOriginale19f62b34dfe0bfdf95075badcb45bc2); ?>
<?php endif; ?>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>



    <!-- Announcements Table -->
    <div class="announcements-table-container">
        <div class="table-header">
            <h2>All Announcements</h2>
            <div class="table-actions">
                <button class="btn btn-primary" onclick="openAddAnnouncementModal()">
                    <i class="fas fa-plus"></i> Add Announcement
                </button>
            </div>
        </div>

        <div class="table-responsive">
            <table class="announcements-table">
                <thead>
                    <tr>
                        <th>Title</th>
                        <th>Content Preview</th>
                        <th>Created Date</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__empty_1 = true; $__currentLoopData = $announcements; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $announcement): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr data-announcement-id="<?php echo e($announcement->id); ?>">
                            <td>
                                <div class="announcement-title">
                                    <?php echo e($announcement->title); ?>

                                </div>
                            </td>
                            <td>
                                <div class="content-preview">
                                    <?php echo e(Str::limit($announcement->content, 100)); ?>

                                </div>
                            </td>
                            <td>
                                <div class="date-info">
                                    <div class="date"><?php echo e($announcement->created_at->format('M d, Y')); ?></div>
                                    <div class="time"><?php echo e($announcement->created_at->format('h:i A')); ?></div>
                                </div>
                            </td>
                            <td>
                                <div class="action-buttons">
                                    <button class="btn-action btn-edit"
                                        onclick="editAnnouncement(<?php echo e($announcement->id); ?>, <?php echo e(json_encode($announcement->title)); ?>, <?php echo e(json_encode($announcement->content)); ?>)"
                                        title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn-action btn-delete"
                                        onclick="deleteAnnouncement(<?php echo e($announcement->id); ?>)" title="Delete">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>

                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="4" class="no-data">
                                <i class="fas fa-bullhorn"></i>
                                <p>No announcements found</p>
                                <small>Click "Add Announcement" to create your first announcement.</small>
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Add/Edit Announcement Modal -->
    <div id="announcementModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modalTitle">Add New Announcement</h2>
                <span class="close" onclick="closeAnnouncementModal()">&times;</span>
            </div>
            <form id="announcementForm">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="title">Title *</label>
                        <input type="text" id="title" name="title" required maxlength="255">
                    </div>

                    <div class="form-group">
                        <label for="content">Content *</label>
                        <textarea id="content" name="content" required rows="6" placeholder="Enter announcement content..."></textarea>
                    </div>


                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="closeAnnouncementModal()">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <span id="submitText">Create Announcement</span>
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Custom Confirmation Modal -->
    <div id="confirmModal" class="modal">
        <div class="modal-content confirmation-modal">
            <div class="modal-header">
                <h2 id="confirmTitle">Confirm Action</h2>
            </div>
            <div class="modal-body">
                <div class="confirmation-content">
                    <i class="fas fa-exclamation-triangle warning-icon"></i>
                    <p id="confirmMessage">Are you sure you want to perform this action?</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeConfirmModal()">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmButton">Delete</button>
            </div>
        </div>
    </div>


<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
    <link rel="stylesheet" href="<?php echo e(asset('css/pages/announcements.css')); ?>">
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
    <script src="<?php echo e(asset('js/admin/announcements.js')); ?>"></script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\adminside\scholarship_1\resources\views/admin/announcements.blade.php ENDPATH**/ ?>