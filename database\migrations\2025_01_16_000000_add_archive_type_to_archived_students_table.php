<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('archived_students', function (Blueprint $table) {
            // Add archive_type column if it doesn't exist
            if (!Schema::hasColumn('archived_students', 'archive_type')) {
                $table->enum('archive_type', ['masterlist', 'inactive'])->default('masterlist')->after('archived_academic_year');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('archived_students', function (Blueprint $table) {
            if (Schema::hasColumn('archived_students', 'archive_type')) {
                $table->dropColumn('archive_type');
            }
        });
    }
};
