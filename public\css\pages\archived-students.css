/* Archived Students Page Styles */

/* Student Categories - matching grantees tab exactly */
.student-categories {
    background-color: white;
    border: 1px solid #ddd;
    padding: 20px;
    margin-bottom: 30px;
}

.category-tabs {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
}

.tab-btn {
    padding: 12px 20px;
    border: 2px solid #052F11;
    background-color: white;
    color: #052F11;
    cursor: pointer;
    font-weight: 500;
    border-radius: 25px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.tab-btn:hover {
    background-color: #052F11;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(5, 47, 17, 0.3);
}

.tab-btn.active {
    background-color: #052F11;
    color: white;
    box-shadow: 0 0 15px rgba(5, 47, 17, 0.4);
    border: 2px solid rgba(5, 47, 17, 0.8);
    font-weight: 600;
}

/* Add a subtle glow effect when tab is being viewed */
.tab-btn.viewing {
    box-shadow: 0 0 10px rgba(5, 47, 17, 0.3);
    border-color: rgba(5, 47, 17, 0.7);
}

/* Enhanced hover animations */
.tab-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.tab-btn:hover::before {
    left: 100%;
}

.archive-actions {
    display: flex;
    gap: 10px;
    align-items: center;
}

/* Remarks badge styling */
.remarks-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    background: #f8f9fa;
    color: #495057;
    border: 1px solid #dee2e6;
    max-width: 150px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.remarks-badge:hover {
    background: #e9ecef;
    white-space: normal;
    overflow: visible;
    text-overflow: unset;
    max-width: none;
    position: relative;
    z-index: 10;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* Export button styling for archives */
.export-btn {
    background: linear-gradient(135deg, #17a2b8, #138496);
    color: white;
    border: none;
    padding: 10px 16px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.export-btn:hover {
    background: linear-gradient(135deg, #138496, #117a8b);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(23, 162, 184, 0.3);
}
