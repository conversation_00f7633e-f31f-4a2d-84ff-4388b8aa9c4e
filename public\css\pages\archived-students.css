/* Archived Students Page Styles */

/* Archive Sub-tabs */
.archive-tabs {
    margin-bottom: 25px;
}

.tab-buttons {
    display: flex;
    gap: 10px;
    background: white;
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.tab-btn {
    background: linear-gradient(135deg, #f8f9fa, #ffffff);
    color: #495057;
    border: 2px solid #e9ecef;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.tab-btn:hover {
    background: linear-gradient(135deg, #e9ecef, #f8f9fa);
    border-color: #1e5631;
    transform: translateY(-1px);
}

.tab-btn.active {
    background: linear-gradient(135deg, #1e5631, #2e7d32);
    color: white;
    border-color: #1e5631;
    box-shadow: 0 4px 12px rgba(30, 86, 49, 0.3);
}

.tab-btn.active:hover {
    background: linear-gradient(135deg, #2e7d32, #388e3c);
}

/* Remarks badge styling */
.remarks-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    background: #f8f9fa;
    color: #495057;
    border: 1px solid #dee2e6;
    max-width: 150px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.remarks-badge:hover {
    background: #e9ecef;
    white-space: normal;
    overflow: visible;
    text-overflow: unset;
    max-width: none;
    position: relative;
    z-index: 10;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* Export button styling for archives */
.export-btn {
    background: linear-gradient(135deg, #17a2b8, #138496);
    color: white;
    border: none;
    padding: 10px 16px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.export-btn:hover {
    background: linear-gradient(135deg, #138496, #117a8b);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(23, 162, 184, 0.3);
}
