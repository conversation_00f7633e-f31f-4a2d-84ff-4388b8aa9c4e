/* Archived Students Page Styles */

/* Grantee Management Header */
.grantee-management-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
    padding: 20px 30px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 25px;
}

.grantee-tabs {
    display: flex;
    gap: 0;
}

.grantee-tab {
    background: #f8f9fa;
    color: #6c757d;
    border: none;
    padding: 12px 24px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: 0;
}

.grantee-tab:first-child {
    border-radius: 8px 0 0 8px;
}

.grantee-tab:last-child {
    border-radius: 0 8px 8px 0;
}

.grantee-tab:hover {
    background: #e9ecef;
    color: #495057;
}

.grantee-tab.active {
    background: #1e5631;
    color: white;
}

.grantee-tab.active:hover {
    background: #2e7d32;
}

.grantee-actions {
    display: flex;
    gap: 10px;
    align-items: center;
}

/* Remarks badge styling */
.remarks-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    background: #f8f9fa;
    color: #495057;
    border: 1px solid #dee2e6;
    max-width: 150px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.remarks-badge:hover {
    background: #e9ecef;
    white-space: normal;
    overflow: visible;
    text-overflow: unset;
    max-width: none;
    position: relative;
    z-index: 10;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* Export button styling for archives */
.export-btn {
    background: linear-gradient(135deg, #17a2b8, #138496);
    color: white;
    border: none;
    padding: 10px 16px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.export-btn:hover {
    background: linear-gradient(135deg, #138496, #117a8b);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(23, 162, 184, 0.3);
}
