/* Application Detail Page Specific Styles */

/* Application Detail Container */
.application-detail-container {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 30px;
}

/* Detail Header */
.detail-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.application-info {
    flex: 1;
}

.application-id {
    font-size: 24px;
    font-weight: 700;
    color: #1e5631;
    margin-bottom: 5px;
}

.application-date {
    font-size: 14px;
    color: #666;
}

/* Scholarship Type Header */
.scholarship-type-header {
    flex: 1;
    text-align: center;
    font-size: 18px;
    font-weight: 600;
    color: #1e5631;
    margin: 0 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Status Section */
.status-section {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 15px;
}

.current-status {
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.current-status.pending {
    background-color: #fff8e1;
    color: #f57f17;
}

.current-status.review {
    background-color: #e3f2fd;
    color: #1565c0;
}

.current-status.approved {
    background-color: #e8f5e9;
    color: #2e7d32;
}

.current-status.rejected {
    background-color: #ffebee;
    color: #c62828;
}

/* Status Form */
.status-form {
    display: flex;
    align-items: center;
    gap: 10px;
}

.status-select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 14px;
    background-color: white;
}

.update-btn {
    background-color: #1e5631;
    color: white;
    border: none;
    border-radius: 5px;
    padding: 8px 15px;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.update-btn:hover {
    background-color: #164023;
}

/* Detail Content */
.detail-content {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 40px;
    margin-top: 20px;
}

.left-column,
.right-column {
    display: flex;
    flex-direction: column;
    gap: 35px;
}

/* Detail Sections */
.detail-section {
    background-color: #fafafa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 25px;
    margin-bottom: 0;
}

.section-title {
    font-size: 18px;
    color: #1e5631;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid #1e5631;
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 600;
}

.section-title i {
    color: #1e5631;
    font-size: 18px;
}

/* Detail Groups */
.detail-group {
    margin-bottom: 18px;
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 20px;
    align-items: start;
    padding: 12px 0;
    border-bottom: 1px solid #e9ecef;
}

.detail-group:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.detail-label {
    font-size: 14px;
    color: #495057;
    font-weight: 600;
    text-align: left;
    line-height: 1.4;
}

.detail-value {
    font-size: 14px;
    color: #212529;
    font-weight: 500;
    word-break: break-word;
    line-height: 1.4;
}

/* Documents Section */
.documents-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.document-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 18px;
    background-color: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    transition: all 0.3s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.document-item:hover {
    background-color: #f8f9fa;
    border-color: #1e5631;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.document-info {
    display: flex;
    align-items: center;
    flex: 1;
}

.document-icon {
    font-size: 24px;
    color: #dc3545;
    margin-right: 12px;
}

.document-details {
    flex: 1;
}

.document-name {
    font-weight: 500;
    color: #333;
    margin-bottom: 4px;
}

.document-size {
    font-size: 12px;
    color: #666;
}

.document-actions {
    display: flex;
    gap: 8px;
}

.btn-download,
.btn-view {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 6px;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 14px;
    border: none;
    cursor: pointer;
}

.btn-download {
    background-color: #28a745;
    color: white;
}

.btn-download:hover {
    background-color: #218838;
    color: white;
}

.btn-view {
    background-color: #007bff;
    color: white;
}

.btn-view:hover {
    background-color: #0056b3;
    color: white;
}

.document-unavailable {
    font-size: 12px;
    color: #dc3545;
    font-style: italic;
}

.no-documents {
    text-align: center;
    padding: 50px 20px;
    color: #6c757d;
    background-color: white;
    border: 2px dashed #dee2e6;
    border-radius: 8px;
}

.no-documents-icon {
    font-size: 3.5rem;
    color: #ced4da;
    margin-bottom: 20px;
}

.no-documents-text {
    font-size: 1.2rem;
    font-weight: 600;
    margin: 0 0 8px 0;
    color: #495057;
}

.no-documents-subtitle {
    font-size: 0.95rem;
    color: #6c757d;
    line-height: 1.4;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 30px;
    padding-top: 30px;
    border-top: 1px solid #eee;
}

.action-btn {
    padding: 12px 24px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
}

.action-btn.approve {
    background-color: #28a745;
    color: white;
}

.action-btn.approve:hover {
    background-color: #218838;
    color: white;
    text-decoration: none;
}

.action-btn.reject {
    background-color: #dc3545;
    color: white;
}

.action-btn.reject:hover {
    background-color: #c82333;
    color: white;
    text-decoration: none;
}

.action-btn.back {
    background-color: #6c757d;
    color: white;
}

.action-btn.back:hover {
    background-color: #5a6268;
    color: white;
    text-decoration: none;
}

/* Success Message */
.success-message {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.success-message i {
    font-size: 16px;
}

/* Document Links */
.document-link {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    color: #1e5631;
    text-decoration: none;
    font-size: 14px;
    padding: 5px 10px;
    border: 1px solid #1e5631;
    border-radius: 5px;
    transition: all 0.2s ease;
}

.document-link:hover {
    background-color: #1e5631;
    color: white;
    text-decoration: none;
}

.document-link i {
    font-size: 12px;
}

/* Requirements Checklist */
.requirements-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.requirements-list li {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
}

.requirements-list li:last-child {
    border-bottom: none;
}

.requirement-status {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    color: white;
}

.requirement-status.complete {
    background-color: #28a745;
}

.requirement-status.incomplete {
    background-color: #dc3545;
}

.requirement-status.pending {
    background-color: #ffc107;
}

/* Timeline */
.application-timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    padding-bottom: 20px;
}

.timeline-item:before {
    content: '';
    position: absolute;
    left: -22px;
    top: 5px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #1e5631;
}

.timeline-item:after {
    content: '';
    position: absolute;
    left: -18px;
    top: 13px;
    width: 1px;
    height: calc(100% - 8px);
    background-color: #ddd;
}

.timeline-item:last-child:after {
    display: none;
}

.timeline-date {
    font-size: 12px;
    color: #666;
    margin-bottom: 5px;
}

.timeline-content {
    font-size: 14px;
    color: #333;
}

/* Responsive Design */
@media (max-width: 768px) {
    .application-detail-container {
        padding: 20px;
    }

    .detail-header {
        flex-direction: column;
        gap: 20px;
        align-items: flex-start;
    }

    .status-section {
        align-items: flex-start;
        width: 100%;
    }

    .detail-content {
        grid-template-columns: 1fr;
        gap: 25px;
    }

    .left-column,
    .right-column {
        gap: 25px;
    }

    .detail-section {
        padding: 20px;
    }

    .detail-group {
        grid-template-columns: 1fr;
        gap: 8px;
        padding: 10px 0;
    }

    .detail-label {
        font-weight: 700;
        color: #1e5631;
    }

    .action-buttons {
        flex-direction: column;
        gap: 10px;
    }

    .action-btn {
        width: 100%;
        justify-content: center;
    }

    .status-form {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }

    .status-select,
    .update-btn {
        width: 100%;
    }
}

@media (max-width: 480px) {
    .application-detail-container {
        padding: 15px;
    }

    .application-id {
        font-size: 20px;
    }

    .section-title {
        font-size: 16px;
    }

    .detail-section {
        padding: 15px;
    }

    .detail-label,
    .detail-value {
        font-size: 13px;
    }

    .detail-group {
        padding: 8px 0;
    }

    .action-btn {
        padding: 10px 20px;
        font-size: 13px;
    }

    .no-documents {
        padding: 30px 15px;
    }

    .no-documents-icon {
        font-size: 2.5rem;
    }
}

/* Document Modal Styles */
.document-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    z-index: 9999;
    justify-content: center;
    align-items: center;
    padding: 20px;
}

.document-modal-content {
    background-color: white;
    border-radius: 10px;
    width: 90%;
    max-width: 1000px;
    height: 90%;
    max-height: 800px;
    display: flex;
    flex-direction: column;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.document-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid #e9ecef;
    background-color: #f8f9fa;
    border-radius: 10px 10px 0 0;
}

.document-modal-header h3 {
    margin: 0;
    color: #1e5631;
    font-size: 18px;
    font-weight: 600;
}

.document-modal-close {
    background: none;
    border: none;
    font-size: 20px;
    color: #6c757d;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.document-modal-close:hover {
    background-color: #e9ecef;
    color: #495057;
}

.document-modal-body {
    flex: 1;
    padding: 0;
    overflow: hidden;
    border-radius: 0 0 10px 10px;
}

#documentViewer {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.document-loading {
    text-align: center;
    color: #6c757d;
}

.document-loading i {
    font-size: 2rem;
    margin-bottom: 15px;
    color: #1e5631;
}

.document-loading p {
    font-size: 16px;
    margin: 0;
}

.document-image {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    border-radius: 0 0 10px 10px;
}

.document-iframe {
    width: 100%;
    height: 100%;
    border-radius: 0 0 10px 10px;
}

.document-preview-unavailable {
    text-align: center;
    padding: 50px 20px;
    color: #6c757d;
}

.document-preview-unavailable i {
    font-size: 4rem;
    color: #dee2e6;
    margin-bottom: 20px;
}

.document-preview-unavailable p {
    font-size: 18px;
    margin-bottom: 25px;
    color: #495057;
}

.document-preview-unavailable .btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    background-color: #1e5631;
    color: white;
    text-decoration: none;
    border-radius: 6px;
    font-weight: 500;
    transition: background-color 0.2s ease;
}

.document-preview-unavailable .btn:hover {
    background-color: #164023;
    color: white;
    text-decoration: none;
}

/* Responsive Modal */
@media (max-width: 768px) {
    .document-modal {
        padding: 10px;
    }

    .document-modal-content {
        width: 95%;
        height: 95%;
    }

    .document-modal-header {
        padding: 15px 20px;
    }

    .document-modal-header h3 {
        font-size: 16px;
    }

    .document-preview-unavailable {
        padding: 30px 15px;
    }

    .document-preview-unavailable i {
        font-size: 3rem;
    }

    .document-preview-unavailable p {
        font-size: 16px;
    }
}

    .subjects-grades-container {
            margin-top: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            overflow: hidden;
            background-color: #f8f9fa;
        }

        .subjects-header {
            display: grid;
            grid-template-columns: 2fr 1fr 1fr;
            gap: 15px;
            padding: 15px;
            background-color: #1e5631;
            color: white;
            font-weight: bold;
            text-align: center;
        }

        .subjects-list {
            max-height: 400px;
            overflow-y: auto;
            background-color: white;
        }

        .subject-row {
            display: grid;
            grid-template-columns: 2fr 1fr 1fr;
            gap: 15px;
            padding: 12px 15px;
            border-bottom: 1px solid #e9ecef;
            align-items: center;
        }

        .subject-row:last-child {
            border-bottom: none;
        }

        .subject-row:hover {
            background-color: #f8f9fa;
        }

        .subject-info {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .subject-code {
            font-weight: bold;
            color: #1e5631;
            font-size: 14px;
        }

        .subject-title {
            color: #666;
            font-size: 13px;
            line-height: 1.3;
        }

        .subject-grade {
            text-align: center;
        }

        .grade-display {
            width: 80px;
            padding: 6px 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            text-align: center;
            background-color: #f8f9fa;
            color: #666;
            font-size: 14px;
        }

        .grade-display.has-grade {
            background-color: #e8f5e8;
            color: #1e5631;
            font-weight: bold;
            border-color: #1e5631;
        }

        .grade-display.no-grade {
            background-color: #f8f9fa;
            color: #999;
        }

        .subject-units {
            text-align: center;
            font-weight: 500;
            color: #333;
        }

        .gwa-summary {
            padding: 15px;
            background-color: #f8f9fa;
            border-top: 2px solid #1e5631;
        }

        .gwa-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .gwa-row:last-child {
            border-bottom: none;
        }

        .gwa-final {
            background-color: #1e5631;
            color: white;
            padding: 12px 15px;
            margin: 10px -15px -15px -15px;
            border-radius: 0 0 8px 8px;
        }

        .gwa-label {
            font-weight: 500;
        }

        .gwa-value {
            font-weight: bold;
            color: #1e5631;
        }

        .gwa-final .gwa-value {
            color: white;
        }

        .loading-subjects, .no-subjects-message {
            text-align: center;
            padding: 40px 20px;
            color: #666;
        }

        .loading-subjects i {
            font-size: 24px;
            margin-bottom: 10px;
            color: #1e5631;
        }

        .no-subjects-message p {
            margin: 0 0 8px 0;
            font-weight: 500;
        }

        .no-subjects-message small {
            color: #999;
            font-size: 12px;
        }

/* Status Badge */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: 2px solid;
}

.status-badge.pending {
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    color: #856404;
    border-color: #ffeaa7;
}

.status-badge.approved {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    color: #155724;
    border-color: #c3e6cb;
}

.status-badge.rejected {
    background: linear-gradient(135deg, #f8d7da, #f5c6cb);
    color: #721c24;
    border-color: #f5c6cb;
}

/* Benefactor Type Badge */
.benefactor-badge {
    background: linear-gradient(135deg, #e3f2fd, #e1f5fe);
    color: #0277bd;
    border: 1px solid #29b6f6;
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: inline-block;
}

/* Classification Badge for Academic */
.classification-badge {
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    color: #856404;
    border: 1px solid #ffeaa7;
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: inline-block;
}
