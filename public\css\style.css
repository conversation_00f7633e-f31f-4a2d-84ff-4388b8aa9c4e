/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

/* Header Styles */
.header {
    background: white;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    padding: 1rem 0;
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 2rem;
}

.logo {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* Logo Image Styles */
.logo-image {
    width: 60px;
    height: 60px;
    object-fit: contain;
    border-radius: 8px;
    background-color: #f8f9fa;
    padding: 5px;
}

.logo-text {
    font-size: 1.2rem;
    font-weight: 600;
    color: #052F11;
}

.logo-text small {
    display: block;
    font-size: 0.9rem;
    font-weight: 400;
    color: #666;
}

.nav {
    display: flex;
    gap: 2rem;
    list-style: none;
}

.nav-link {
    color: #333;
    text-decoration: none;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 5px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.nav-link:hover, .nav-link.active {
    background: #052F11;
    color: white;
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, #052F11 0%, #052F11 100%);
    color: white;
    padding: 4rem 0;
    text-align: center;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.hero h1 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    font-weight: 700;
}

.hero h2 {
    font-size: 1.8rem;
    margin-bottom: 1.5rem;
    font-weight: 600;
    color: #f0f8ff;
}

.hero p {
    font-size: 1.2rem;
    opacity: 0.9;
    max-width: 800px;
    margin: 0 auto;
}

/* Main Content */
.main-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 3rem 2rem;
}

.content-section {
    display: none;
    animation: fadeIn 0.5s ease-in;
}

.content-section.active {
    display: block;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.section-title {
    font-size: 2rem;
    color: #052F11;
    margin-bottom: 1.5rem;
    text-align: center;
}

.cards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.card-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #052F11, #052F11);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    color: white;
    font-size: 2rem;
}

.card h3 {
    color: #052F11;
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.card h4 {
    color: #052F11;
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.card p {
    color: #666;
    line-height: 1.6;
}

/* Buttons */
.btn {
    background: linear-gradient(135deg, #052F11, #052F11);
    color: white;
    padding: 12px 30px;
    border: none;
    border-radius: 25px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    margin: 10px;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(5, 47, 17, 0.4);
}

.btn-secondary {
    background: linear-gradient(135deg, #007bff, #0056b3);
}

.btn-secondary:hover {
    box-shadow: 0 5px 15px rgba(0, 123, 255, 0.4);
}

.services-buttons {
    text-align: center;
    margin-top: 2rem;
}

.services-buttons h3 {
    color: #052F11;
    margin-bottom: 1rem;
}

/* Info Grid */
.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
}

.info-item {
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.info-item:hover {
    transform: translateY(-3px);
}

.info-item h4 {
    color: #052F11;
    margin-bottom: 0.5rem;
}

.info-item p {
    color: #666;
    line-height: 1.6;
}

/* Footer */
.footer {
    background: #052F11;
    color: white;
    text-align: center;
    padding: 2rem 0;
    margin-top: 3rem;
}

.footer-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.footer p {
    margin-bottom: 0.5rem;
}

.footer p:last-child {
    opacity: 0.8;
    font-size: 0.9rem;
}

/* Bottom Action Buttons */
.bottom-actions {
    background-color: #f8f9fa;
    padding: 3rem 0;
    text-align: center;
    border-top: 1px solid #e9ecef;
}

.bottom-actions .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.action-buttons {
    display: flex;
    justify-content: center;
    gap: 1.5rem;
    margin-top: 1.5rem;
}

@media (max-width: 480px) {
    .action-buttons {
        flex-direction: column;
        align-items: center;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-container {
        flex-direction: column;
        gap: 1rem;
    }

    .nav {
        flex-wrap: wrap;
        justify-content: center;
        gap: 1rem;
    }

    .hero h1 {
        font-size: 2rem;
    }

    .hero h2 {
        font-size: 1.5rem;
    }

    .hero p {
        font-size: 1rem;
    }

    .main-content {
        padding: 2rem 1rem;
    }

    .cards-grid {
        grid-template-columns: 1fr;
    }

    .info-grid {
        grid-template-columns: 1fr;
    }

    .logo-text {
        font-size: 1rem;
    }

    .logo-image {
        width: 50px;
        height: 50px;
    }
}

@media (max-width: 480px) {
    .hero {
        padding: 2rem 0;
    }

    .hero h1 {
        font-size: 1.5rem;
    }

    .hero h2 {
        font-size: 1.2rem;
    }

    .section-title {
        font-size: 1.5rem;
    }

    .card {
        padding: 1.5rem;
    }

    .card-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }
}

