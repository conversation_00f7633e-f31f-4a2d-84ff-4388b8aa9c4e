/* Scholarships Page - Clean Styles */

/* Quick Actions */
.quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 30px;
}

.action-card {
    background-color: white;
    border: 1px solid #ddd;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    text-decoration: none;
    color: inherit;
}

.action-card:hover {
    background-color: #f5f5f5;
    text-decoration: none;
    color: inherit;
}

.action-icon {
    font-size: 24px;
    color: #333;
    margin-bottom: 10px;
}

.action-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 5px;
    color: #333;
}

.action-description {
    font-size: 12px;
    color: #666;
}

/* Scholarship Programs Table */
.scholarship-programs {
    background-color: white;
    border: 1px solid #ddd;
}

.table-header {
    background-color: #f8f9fa;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #ddd;
}

.table-header h3 {
    margin: 0;
    color: #333;
    font-size: 18px;
}

.add-scholarship-btn {
    background: #052F11;
    color: white;
    border: 1px solid #052F11;
    padding: 10px 16px;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.add-scholarship-btn:hover {
    background: rgba(5, 47, 17, 0.8);
    color: white;
    text-decoration: none;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(5, 47, 17, 0.3);
}

.update-semester-year-btn {
    background: rgba(5, 47, 17, 0.7);
    color: white;
    border: 1px solid rgba(5, 47, 17, 0.7);
    padding: 10px 16px;
    font-size: 14px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.update-semester-year-btn:hover {
    background: #052F11;
    color: white;
    text-decoration: none;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(5, 47, 17, 0.3);
}

/* Export Button */
.export-btn {
    background: rgba(5, 47, 17, 0.6);
    color: white;
    border: 1px solid rgba(5, 47, 17, 0.6);
    padding: 10px 16px;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    margin-right: 10px;
    transition: all 0.3s ease;
}

.export-btn:hover {
    background: #052F11;
    color: white;
    text-decoration: none;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(5, 47, 17, 0.3);
}

/* Scholarships Table */
.scholarships-table {
    width: 100%;
    border-collapse: collapse;
}

.scholarships-table th {
    background-color: #f8f9fa;
    padding: 15px;
    text-align: left;
    font-weight: 600;
    color: #333;
    border-bottom: 1px solid #ddd;
    font-size: 14px;
}

.scholarships-table td {
    padding: 15px;
    border-bottom: 1px solid #eee;
    font-size: 14px;
    color: #333;
}

.scholarships-table tr:last-child td {
    border-bottom: none;
}

/* Grantees Count Display */
.grantees-count {
    text-align: center;
}

.count-number {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    display: block;
}

.count-label {
    font-size: 12px;
    color: #666;
}

/* Simple Badges */
.status-badge {
    padding: 4px 8px;
    font-size: 12px;
    background: #f5f5f5;
    color: #333;
    border: 1px solid #ddd;
}

/* Scholarship Name Cell */
.scholarship-name-cell {
    display: flex;
    align-items: center;
    gap: 8px;
}

/* Scholarship Type Indicators */
.scholarship-type-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #666;
}

/* Scholarship Type Badges */
.scholarship-type {
    padding: 4px 8px;
    font-size: 12px;
    background: #f5f5f5;
    color: #333;
    border: 1px solid #ddd;
}

/* Semester Badge */
.semester-badge {
    padding: 4px 8px;
    font-size: 12px;
    background: #f5f5f5;
    color: #333;
    border: 1px solid #ddd;
}

/* Academic Year */
.academic-year {
    padding: 4px 8px;
    font-size: 12px;
    background: #f5f5f5;
    color: #333;
    border: 1px solid #ddd;
}

/* Slots Display */
.scholarships-table td strong {
    color: #333;
    font-size: 16px;
}

.scholarships-table td small.text-muted {
    color: #666;
    font-size: 12px;
    margin-left: 2px;
}

/* Empty State for Table */
.scholarships-table .empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #666;
}

.scholarships-table .empty-state i {
    font-size: 48px;
    color: #ccc;
    margin-bottom: 15px;
    display: block;
}

.scholarships-table .empty-state h3 {
    margin: 0 0 10px;
    color: #999;
    font-size: 18px;
}

.scholarships-table .empty-state p {
    margin: 0;
    font-size: 14px;
}

/* Status Badges */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 10px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-badge.pending {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.status-badge.approved {
    background-color: rgba(5, 47, 17, 0.1);
    color: #052F11;
    border: 1px solid rgba(5, 47, 17, 0.3);
}

.status-badge.rejected {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.status-badge.active {
    background-color: rgba(5, 47, 17, 0.1);
    color: #052F11;
    border: 1px solid rgba(5, 47, 17, 0.3);
}

/* Action Buttons */
.action-btn {
    padding: 6px 10px;
    margin: 0 2px;
    border: 1px solid #052F11;
    cursor: pointer;
    font-size: 12px;
    background-color: #052F11;
    color: white;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 4px;
    transition: all 0.2s ease;
    border-radius: 3px;
}

.action-btn:hover {
    background-color: rgba(5, 47, 17, 0.8);
    color: white;
    text-decoration: none;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(5, 47, 17, 0.3);
}

.action-btn.view {
    background-color: #052F11;
    border-color: #052F11;
}

.action-btn.edit {
    background-color: rgba(5, 47, 17, 0.7);
    border-color: rgba(5, 47, 17, 0.7);
}

.action-btn.delete {
    background-color: #dc3545;
    border-color: #dc3545;
}

.action-btn.delete:hover {
    background-color: #c82333;
    border-color: #c82333;
}
/* Empty State */
.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #666;
}

.empty-state h3 {
    margin: 0 0 10px;
    color: #999;
}

.empty-state p {
    margin: 0;
    font-size: 14px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .quick-actions {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 10px;
    }

    .action-card {
        padding: 15px;
    }

    .action-icon {
        font-size: 24px;
    }

    .action-title {
        font-size: 14px;
    }

    .action-description {
        font-size: 11px;
    }

    .table-header {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }

    .add-scholarship-btn {
        width: 100%;
        justify-content: center;
    }

    .scholarships-table {
        font-size: 12px;
    }

    .scholarships-table th,
    .scholarships-table td {
        padding: 10px 8px;
    }

    /* Hide less important columns on mobile */
    .scholarships-table th:nth-child(4),
    .scholarships-table td:nth-child(4) {
        display: none;
    }

    .scholarship-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }
}

@media (max-width: 480px) {
    .quick-actions {
        grid-template-columns: 1fr;
    }

    .scholarship-stats {
        grid-template-columns: 1fr;
    }

    .table-header {
        padding: 15px;
    }

    .table-header h3 {
        font-size: 16px;
    }

    .add-scholarship-btn {
        padding: 8px 12px;
        font-size: 12px;
    }
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(2px);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    border-radius: 12px;
    width: 90%;
    max-width: 600px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    background: linear-gradient(135deg, #1e5631, #2e7d32);
    color: white;
    padding: 20px 25px;
    border-radius: 12px 12px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
}

.close {
    color: white;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.2s ease;
    line-height: 1;
}

.close:hover {
    color: #ffcdd2;
}

.modal-body {
    padding: 25px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
    font-size: 14px;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 12px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
    background-color: #fafafa;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #1e5631;
    background-color: white;
    box-shadow: 0 0 0 3px rgba(30, 86, 49, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

.modal-footer {
    padding: 20px 25px;
    border-top: 1px solid #e0e0e0;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    background-color: #fafafa;
    border-radius: 0 0 12px 12px;
}

.modal-footer .btn-secondary,
.modal-footer .btn-primary {
    padding: 10px 20px;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
    font-size: 14px;
}

.modal-footer .btn-secondary {
    background-color: #6c757d;
    color: white;
}

.modal-footer .btn-secondary:hover {
    background-color: #5a6268;
    transform: translateY(-1px);
}

.modal-footer .btn-primary {
    background: linear-gradient(135deg, #1e5631, #2e7d32);
    color: white;
}

.modal-footer .btn-primary:hover {
    background: linear-gradient(135deg, #164023, #1b5e20);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(30, 86, 49, 0.3);
}

.modal-footer .btn-primary:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}



/* Confirmation Modal Styles */
.confirmation-modal {
    max-width: 500px;
    margin: 15% auto;
}

.confirmation-content {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    padding: 20px 0;
}

.warning-icon {
    font-size: 2.5rem;
    color: #f39c12;
    flex-shrink: 0;
    margin-top: 4px;
}

.confirmation-text p {
    font-size: 1.1rem;
    color: #2c3e50;
    margin: 0;
    line-height: 1.6;
}

/* Update Options Styles */
.update-selection h4 {
    margin: 0 0 20px 0;
    color: #333;
    font-size: 16px;
    font-weight: 600;
}

.update-options {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin: 20px 0;
}

.update-option-card {
    background: linear-gradient(135deg, #f8f9fa, #ffffff);
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.update-option-card:hover {
    border-color: #1e5631;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(30, 86, 49, 0.15);
    background: linear-gradient(135deg, #ffffff, #f8f9fa);
}

.update-option-card:hover .option-icon {
    transform: scale(1.1);
    color: #1e5631;
}

.update-option-card.selected {
    border-color: #1e5631;
    background: linear-gradient(135deg, #e8f5e8, #f1f8e9);
    box-shadow: 0 4px 15px rgba(30, 86, 49, 0.2);
}

.update-option-card.selected .option-icon {
    color: #1e5631;
    transform: scale(1.05);
}

.option-radio {
    position: absolute;
    top: 15px;
    right: 15px;
}

.option-radio input[type="radio"] {
    width: 18px;
    height: 18px;
    accent-color: #1e5631;
    cursor: pointer;
}

.option-icon {
    font-size: 32px;
    color: #6c757d;
    margin-bottom: 15px;
    transition: all 0.3s ease;
}

.option-content h3 {
    margin: 0 0 10px 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.option-content p {
    margin: 0;
    font-size: 14px;
    color: #666;
    line-height: 1.4;
}

.option-content span {
    font-weight: 600;
    color: #1e5631;
}

/* Modal Responsive Design */
@media (max-width: 768px) {
    .modal-content {
        width: 95%;
        margin: 10% auto;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .modal-header {
        padding: 15px 20px;
    }

    .modal-body {
        padding: 20px;
    }

    .modal-footer {
        padding: 15px 20px;
        flex-direction: column;
    }

    .modal-footer .btn-secondary,
    .modal-footer .btn-primary {
        width: 100%;
        justify-content: center;
    }

    .update-options {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .update-option-card {
        padding: 15px;
    }

    .option-icon {
        font-size: 28px;
        margin-bottom: 10px;
    }

    .option-content h3 {
        font-size: 14px;
    }

    .option-content p {
        font-size: 12px;
    }
}

/* Modal Styles for Scholarships Page */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    animation: fadeIn 0.3s ease;
}

.modal[style*="display: block"],
.modal.modal-show {
    display: flex !important;
}

.modal .modal-content {
    background: white;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    animation: slideIn 0.3s ease;
}

.modal .modal-header {
    padding: 20px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal .modal-header h2 {
    margin: 0;
    color: #333;
    font-size: 20px;
}

.modal .close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.modal .close:hover {
    background-color: #f5f5f5;
    color: #333;
}

.modal .modal-body {
    padding: 20px;
}

.modal .modal-footer {
    padding: 20px;
    border-top: 1px solid #eee;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* Form Styles for Modal */
.modal .form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-bottom: 15px;
}

.modal .form-group {
    margin-bottom: 15px;
}

.modal .form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #333;
}

.modal .form-group input,
.modal .form-group select,
.modal .form-group textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 14px;
    transition: border-color 0.3s ease;
    box-sizing: border-box;
}

.modal .form-group input:focus,
.modal .form-group select:focus,
.modal .form-group textarea:focus {
    outline: none;
    border-color: #1e5631;
    box-shadow: 0 0 0 2px rgba(30, 86, 49, 0.1);
}

/* Modal Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideIn {
    from {
        transform: translateY(-50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Modal Responsive Design */
@media (max-width: 768px) {
    .modal .modal-content {
        width: 95%;
        margin: 20px;
    }

    .modal .modal-header,
    .modal .modal-body,
    .modal .modal-footer {
        padding: 15px;
    }

    .modal .form-row {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .modal .modal-footer {
        flex-direction: column;
    }

    .modal .modal-footer button {
        width: 100%;
        margin-bottom: 10px;
    }

    .modal .modal-footer button:last-child {
        margin-bottom: 0;
    }
}
