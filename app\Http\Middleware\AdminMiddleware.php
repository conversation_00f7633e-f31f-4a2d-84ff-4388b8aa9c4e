<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class AdminMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if user is authenticated
        if (!Auth::check()) {
            Log::warning('Unauthorized admin access attempt', [
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'url' => $request->fullUrl()
            ]);
            return redirect()->route('login')->with('error', 'Please log in to access the admin panel.');
        }

        // Check if user has administrator role
        $user = Auth::user();
        if ($user->role !== 'administrator') {
            Log::warning('Non-admin user attempted to access admin area', [
                'user_id' => $user->id,
                'user_role' => $user->role,
                'ip' => $request->ip(),
                'url' => $request->fullUrl()
            ]);
            $message = "🚫 Access Denied: Administrator privileges required. You are currently logged in as a {$user->role}. Please log in with an administrator account to access the admin panel.";
            return redirect()->route('student.dashboard')->with('error', $message);
        }

        // Log successful admin access for security monitoring
        Log::info('Admin access granted', [
            'user_id' => $user->id,
            'user_name' => $user->name,
            'ip' => $request->ip(),
            'url' => $request->fullUrl()
        ]);

        return $next($request);
    }
}
