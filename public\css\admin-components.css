/* Admin Components CSS - Import all component styles */

/* Import component stylesheets */
@import url('./components/breadcrumb.css');
@import url('./components/pagination.css');
@import url('./components/forms.css');
@import url('./components/cards.css');

/* Additional component-specific styles can be added here */

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 10px; }
.mb-2 { margin-bottom: 20px; }
.mb-3 { margin-bottom: 30px; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 10px; }
.mt-2 { margin-top: 20px; }
.mt-3 { margin-top: 30px; }

.p-0 { padding: 0; }
.p-1 { padding: 10px; }
.p-2 { padding: 20px; }
.p-3 { padding: 30px; }

.d-flex { display: flex; }
.d-block { display: block; }
.d-none { display: none; }

.justify-content-center { justify-content: center; }
.justify-content-between { justify-content: space-between; }
.justify-content-end { justify-content: flex-end; }

.align-items-center { align-items: center; }
.align-items-start { align-items: flex-start; }
.align-items-end { align-items: flex-end; }

.gap-1 { gap: 10px; }
.gap-2 { gap: 20px; }
.gap-3 { gap: 30px; }

.w-100 { width: 100%; }
.h-100 { height: 100%; }

/* Color utilities */
.text-primary { color: #1e5631; }
.text-secondary { color: #6c757d; }
.text-success { color: #28a745; }
.text-danger { color: #dc3545; }
.text-warning { color: #ffc107; }
.text-muted { color: #666; }

.bg-primary { background-color: #1e5631; }
.bg-secondary { background-color: #6c757d; }
.bg-success { background-color: #28a745; }
.bg-danger { background-color: #dc3545; }
.bg-warning { background-color: #ffc107; }
.bg-light { background-color: #f8f9fa; }
.bg-white { background-color: white; }

/* Border utilities */
.border { border: 1px solid #ddd; }
.border-top { border-top: 1px solid #ddd; }
.border-bottom { border-bottom: 1px solid #ddd; }
.border-left { border-left: 1px solid #ddd; }
.border-right { border-right: 1px solid #ddd; }

.rounded { border-radius: 5px; }
.rounded-lg { border-radius: 10px; }
.rounded-circle { border-radius: 50%; }

/* Shadow utilities */
.shadow-sm { box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1); }
.shadow { box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); }
.shadow-lg { box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15); }

/* Responsive utilities */
@media (max-width: 768px) {
    .d-md-none { display: none; }
    .d-md-block { display: block; }
    .d-md-flex { display: flex; }
    
    .text-md-center { text-align: center; }
    .text-md-left { text-align: left; }
    
    .mb-md-1 { margin-bottom: 10px; }
    .mb-md-2 { margin-bottom: 20px; }
    
    .p-md-1 { padding: 10px; }
    .p-md-2 { padding: 20px; }
}
