/* Dashboard Page Specific Styles */

/* Dashboard Header */
.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.dashboard-header h1 {
    color: #333;
    font-size: 28px;
    margin: 0;
}

.dashboard-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

.refresh-btn {
    background: #1e5631;
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.refresh-btn:hover {
    background: #164023;
    transform: translateY(-1px);
}

.refresh-btn i {
    transition: transform 0.3s ease;
}

.refresh-btn:hover i {
    transform: rotate(180deg);
}

.date {
    color: #666;
    font-size: 14px;
}

/* Statistics Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
}

.stat-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
}

.stat-title {
    font-size: 14px;
    color: #666;
    margin: 0;
}

.stat-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 18px;
}

.stat-icon.total {
    background-color: #1e5631;
}

.stat-icon.pending {
    background-color: #f57f17;
}

.stat-icon.approved {
    background-color: #2e7d32;
}

.stat-icon.rejected {
    background-color: #c62828;
}

.stat-value {
    font-size: 28px;
    font-weight: 700;
    color: #333;
    margin: 0 0 5px;
}

.stat-change {
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.stat-change.positive {
    color: #2e7d32;
}

.stat-change.negative {
    color: #c62828;
}

.stat-change.neutral {
    color: #666;
}

/* View All Links */
.view-all {
    color: #1e5631;
    text-decoration: none;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.view-all:hover {
    text-decoration: underline;
}

/* Applications Table */
.applications-table {
    width: 100%;
    border-collapse: collapse;
}

.applications-table th {
    background-color: #f5f5f5;
    padding: 12px 15px;
    text-align: left;
    font-size: 14px;
    color: #555;
    font-weight: 600;
    border-bottom: 1px solid #ddd;
}

.applications-table td {
    padding: 15px;
    border-bottom: 1px solid #eee;
    font-size: 14px;
    color: #333;
}

.applications-table tr:last-child td {
    border-bottom: none;
}

/* Status Badges */
.status {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
}

.status.pending {
    background-color: #fff8e1;
    color: #f57f17;
}

.status.review {
    background-color: #e3f2fd;
    color: #1565c0;
}

.status.approved {
    background-color: #e8f5e9;
    color: #2e7d32;
}

.status.rejected {
    background-color: #ffebee;
    color: #c62828;
}

/* Action Buttons */
.action-btn {
    display: inline-block;
    padding: 5px 10px;
    background-color: #1e5631;
    color: white;
    border-radius: 5px;
    text-decoration: none;
    font-size: 12px;
}

.action-btn:hover {
    background-color: #164023;
}

.action-btn.delete {
    background-color: #dc3545;
    margin-left: 5px;
}

.action-btn.delete:hover {
    background-color: #c82333;
}



/* Charts Section */
.charts-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 30px;
}

.chart-container {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.chart-container:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.chart-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 20px;
    color: #333;
    display: flex;
    align-items: center;
    gap: 10px;
}

.chart-title::before {
    content: '';
    width: 4px;
    height: 20px;
    background-color: #1e5631;
    border-radius: 2px;
}

.chart-canvas {
    position: relative;
    height: 300px;
}

/* Responsive chart layout */
@media (max-width: 1024px) {
    .charts-section {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .chart-canvas {
        height: 250px;
    }

    .chart-container {
        padding: 15px;
    }

    .chart-title {
        font-size: 16px;
        margin-bottom: 15px;
    }
}

/* Quick Actions */
.quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 30px;
}

.action-card {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
    text-align: center;
    transition: transform 0.3s ease;
    cursor: pointer;
    text-decoration: none;
    color: inherit;
}

.action-card:hover {
    transform: translateY(-5px);
    text-decoration: none;
    color: inherit;
}

.action-icon {
    font-size: 32px;
    color: #1e5631;
    margin-bottom: 10px;
}

.action-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 5px;
}

.action-description {
    font-size: 12px;
    color: #666;
}

/* Student Categories */
.student-categories {
    margin-bottom: 30px;
}

.category-tabs {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.tab-btn {
    padding: 12px 20px;
    border: 2px solid #1e5631;
    background-color: white;
    color: #1e5631;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.tab-btn.active,
.tab-btn:hover {
    background-color: #1e5631;
    color: white;
}

.student-table-container {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.table-header {
    background-color: #f8f9fa;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #eee;
}

.table-header h3 {
    margin: 0;
    color: #333;
}

.table-actions {
    display: flex;
    gap: 10px;
}

/* Archive Tables */
.archive-table,
.students-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

.archive-table th,
.archive-table td,
.students-table th,
.students-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.archive-table th,
.students-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #333;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .admin-container {
        grid-template-columns: 1fr;
    }

    .admin-sidebar {
        margin-bottom: 30px;
    }

    .category-tabs {
        flex-wrap: wrap;
    }

    .tab-btn {
        padding: 10px 15px;
        font-size: 14px;
    }

    .table-header {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }

    .table-actions {
        width: 100%;
        justify-content: flex-end;
    }
}

@media (max-width: 480px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }
}

/* GWA Requirements Styling */
.gwa-requirements {
    margin-top: 15px;
    padding: 10px;
    background-color: #f8f9fa;
    border-left: 4px solid #007bff;
    border-radius: 4px;
}

.gwa-requirements .form-help-text {
    margin: 0;
    color: #495057;
    line-height: 1.4;
}
