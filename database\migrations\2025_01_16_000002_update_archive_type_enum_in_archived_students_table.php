<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update any existing 'graduates' records to 'inactive'
        DB::table('archived_students')
            ->where('archive_type', 'graduates')
            ->update(['archive_type' => 'inactive']);

        // For MySQL, we need to modify the enum column
        if (Schema::hasColumn('archived_students', 'archive_type')) {
            DB::statement("ALTER TABLE archived_students MODIFY COLUMN archive_type ENUM('masterlist', 'inactive') DEFAULT 'masterlist'");
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert back to the original enum values
        if (Schema::hasColumn('archived_students', 'archive_type')) {
            DB::statement("ALTER TABLE archived_students MODIFY COLUMN archive_type ENUM('masterlist', 'graduates') DEFAULT 'masterlist'");
        }

        // Update any 'inactive' records back to 'graduates'
        DB::table('archived_students')
            ->where('archive_type', 'inactive')
            ->update(['archive_type' => 'graduates']);
    }
};
