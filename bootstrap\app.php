<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use App\Http\Middleware\StudentMiddleware;
use App\Http\Middleware\AdminMiddleware;
use App\Http\Middleware\SecurityHeadersMiddleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__ . '/../routes/web.php',
        commands: __DIR__ . '/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        // Global middleware
        $middleware->append(SecurityHeadersMiddleware::class);

        // Route middleware aliases
        $middleware->alias([
            'student' => StudentMiddleware::class,
            'admin' => AdminMiddleware::class,
            'ensure.application.ownership' => \App\Http\Middleware\EnsureStudentOwnsApplication::class,
            'security.headers' => SecurityHeadersMiddleware::class,
        ]);

        // Apply rate limiting to API routes
        $middleware->throttleApi();
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->create();
